import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { fetchLeadsByUser, updateLeadStatus, updateLead } from '../lib/leadService';
import { LEAD_STATUSES } from '../constants/leadStatus';
import { Lead } from '../components/LeadCard';
import { Spinner } from '../components/Spinner';
import { toast } from 'react-hot-toast';
import { getQuoteClient } from '../lib/supabaseManager';
import { Phone, Search, Filter, RefreshCw, ChevronLeft, ChevronRight, MoreVertical, Edit, Trash2, Plus, Minus, FileText, MessageSquare, Lightbulb, Star, Heart, Bookmark } from 'lucide-react';
import { supabase } from '../lib/supabaseClient';
import EditLeadModal from '../components/EditLeadModal';
import WhatsAppIcon from '../components/WhatsappIcon';
import NotesModal, { Note } from '../components/NotesModal';

const LeadsTable: React.FC = () => {
  const { user } = useAuth();
  const [leads, setLeads] = useState<Lead[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [packageTypeFilter, setPackageTypeFilter] = useState<string>('ALL');
  const [monthFilter, setMonthFilter] = useState<string>('ALL');
  const [destinationFilter, setDestinationFilter] = useState<string>('ALL');
  const [uniqueDestinations, setUniqueDestinations] = useState<string[]>([]);
  const [nightsFilter, setNightsFilter] = useState<string>('ALL');
  const [adultsFilter, setAdultsFilter] = useState<string>('');
  const [childrenFilter, setChildrenFilter] = useState<string>('');
  const [infantsFilter, setInfantsFilter] = useState<string>('');
  const [isNightsCustom, setIsNightsCustom] = useState<boolean>(false);
  const [isAdultsCustom, setIsAdultsCustom] = useState<boolean>(false);
  const [isChildrenCustom, setIsChildrenCustom] = useState<boolean>(false);
  const [isInfantsCustom, setIsInfantsCustom] = useState<boolean>(false);
  const [assignedToFilter, setAssignedToFilter] = useState<string>('ALL');
  const [updatingLeadId, setUpdatingLeadId] = useState<string | null>(null);
  const [editingLead, setEditingLead] = useState<Lead | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(50);
  const [userProfiles, setUserProfiles] = useState<{ [key: string]: string }>({});
  const [quotesData, setQuotesData] = useState<{ [key: string]: any[] }>({});
  const [zoomLevel, setZoomLevel] = useState(100);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [notesModal, setNotesModal] = useState<{
    isOpen: boolean;
    leadId: string;
    leadName: string;
    notes: Note[];
  }>({
    isOpen: false,
    leadId: '',
    leadName: '',
    notes: [],
  });
  const [leadNotes, setLeadNotes] = useState<{ [leadId: string]: Note[] }>({});
  const [hoveredNoteId, setHoveredNoteId] = useState<string | null>(null);

  const normalizePhoneNumber = (phone: string) => {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');
    return cleaned.slice(-10);
  };

  const getWhatsAppFormattedNumber = (phone: string): string => {
    if (!phone) return '';
    let cleaned = phone.replace(/\D/g, ''); // Remove all non-digits

    // If it starts with a '+' (which would have been removed by \D/g, but let's be safe)
    if (phone.startsWith('+')) {
      cleaned = phone.replace(/\D/g, ''); // Re-clean to ensure no other chars, but keep digits
    } else if (cleaned.length === 10) {
      // Assume Indian number if 10 digits and no country code
      cleaned = '91' + cleaned;
    }
    return cleaned;
  };

  const handleDeleteLead = async (leadId: string) => {
    if (window.confirm('Are you sure you want to delete this lead?')) {
      try {
        const { error } = await supabase.from('leads').delete().eq('id', leadId);
        if (error) {
          throw error;
        }
        setLeads(leads.filter(lead => lead.id !== leadId));
        toast.success('Lead deleted successfully');
      } catch (error: any) {
        toast.error(error.message || 'Failed to delete lead');
      }
    }
  };

  // Note management functions
  const handleAddNote = async (leadId: string, leadName: string) => {
    const notes = await loadNotesForLead(leadId);
    setNotesModal({
      isOpen: true,
      leadId,
      leadName,
      notes,
    });
  };

  const loadNotesForLead = async (leadId: string): Promise<Note[]> => {
    try {
      const { data, error } = await supabase
        .from('notes')
        .select('*')
        .eq('lead_id', leadId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      const notes = data || [];
      setLeadNotes(prev => ({ ...prev, [leadId]: notes }));
      return notes;
    } catch (error: any) {
      console.error('Error loading notes:', error);
      toast.error('Failed to load notes');
      return [];
    }
  };

  const handleSaveNote = async (leadId: string, noteInfo: string, noteId?: string) => {
    try {
      if (noteId) {
        // Update existing note
        const { error } = await supabase
          .from('notes')
          .update({ info: noteInfo, updated_at: new Date().toISOString() })
          .eq('id', noteId);

        if (error) {
          throw error;
        }
        toast.success('Note updated successfully');
      } else {
        // Create new note
        const { error } = await supabase
          .from('notes')
          .insert({ lead_id: leadId, info: noteInfo });

        if (error) {
          throw error;
        }
        toast.success('Note added successfully');
      }

      // Refresh notes for this lead
      await loadNotesForLead(leadId);
    } catch (error: any) {
      toast.error(error.message || 'Failed to save note');
      throw error;
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    try {
      const { error } = await supabase
        .from('notes')
        .delete()
        .eq('id', noteId);

      if (error) {
        throw error;
      }

      toast.success('Note deleted successfully');

      // Refresh notes for the current lead
      if (notesModal.leadId) {
        await loadNotesForLead(notesModal.leadId);
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete note');
      throw error;
    }
  };

  const closeNotesModal = () => {
    setNotesModal({
      isOpen: false,
      leadId: '',
      leadName: '',
      notes: [],
    });
  };

  // Random icon selection for notes
  const noteIcons = [FileText, MessageSquare, Lightbulb, Star, Heart, Bookmark];

  const getRandomNoteIcon = (noteContent: string) => {
    // Use note content to generate consistent random icon
    const hash = noteContent.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    const iconIndex = Math.abs(hash) % noteIcons.length;
    return noteIcons[iconIndex];
  };

  const renderNoteIcons = (leadId: string, leadName: string) => {
    const notes = leadNotes[leadId] || [];
    if (notes.length === 0) return null;

    return notes.slice(0, 3).map((note) => {
      const IconComponent = getRandomNoteIcon(note.info);
      const truncatedNote = note.info.length > 50 ? note.info.substring(0, 50) + '...' : note.info;

      return (
        <div 
          key={note.id} 
          className="relative"
          onMouseEnter={() => setHoveredNoteId(note.id)}
          onMouseLeave={() => setHoveredNoteId(null)}
        >
          <button
            onClick={() => handleAddNote(leadId, leadName)}
            className="p-1 text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
            title="Click to view/edit notes"
          >
            <IconComponent className="w-3 h-3" />
          </button>

          {/* Tooltip */}
          {hoveredNoteId === note.id && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg transition-opacity duration-200 pointer-events-none z-10 max-w-xs">
              <div className="whitespace-pre-wrap break-words">{truncatedNote}</div>
              <div className="text-xs text-gray-300 mt-1">
                {new Date(note.created_at).toLocaleDateString()}
              </div>
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
            </div>
          )}
        </div>
      );
    });
  };

  useEffect(() => {
    if (user?.id) {
      fetchLeads();
    }
  }, [user?.id]);

  // Fetch user profiles and notes after leads are loaded
  useEffect(() => {
    if (leads.length > 0) {
      fetchUserProfiles();
      fetchQuotesForCustomers();
      loadAllLeadNotes();

      // Extract unique destinations
      const destinations = Array.from(new Set(leads.map(lead => lead.destination).filter(Boolean) as string[]));
      setUniqueDestinations(destinations.sort());
    }
  }, [leads]);

  const loadAllLeadNotes = async () => {
    try {
      const leadIds = leads.map(lead => lead.id);
      if (leadIds.length === 0) return;

      const { data, error } = await supabase
        .from('notes')
        .select('*')
        .in('lead_id', leadIds)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Group notes by lead_id
      const notesByLead: { [leadId: string]: Note[] } = {};
      (data || []).forEach(note => {
        if (!notesByLead[note.lead_id]) {
          notesByLead[note.lead_id] = [];
        }
        notesByLead[note.lead_id].push(note);
      });

      setLeadNotes(notesByLead);
    } catch (error: any) {
      console.error('Error loading all lead notes:', error);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdown && !(event.target as HTMLElement).closest('.relative')) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openDropdown]);

  const fetchLeads = async () => {
    if (!user?.id) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await fetchLeadsByUser(user.id);
      if (result.success && result.data) {
        setLeads(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch leads');
      }
    } catch (error: any) {
      console.error('Error fetching leads:', error);
      setError(error.message || 'Failed to load leads');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUserProfiles = async () => {
    try {
      const userIds = [...new Set(leads.map(lead => lead.assigned_to).filter(Boolean))] as string[];
      if (userIds.length === 0) return;

      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, full_name')
        .in('id', userIds);

      if (profilesError) {
        throw profilesError;
      }

      const profilesMap: { [key: string]: string } = {};
      if (profilesData) {
        profilesData.forEach((profile: any) => {
          profilesMap[profile.id] = profile.full_name || 'Unknown';
        });
      }
      
      setUserProfiles(profilesMap);
    } catch (error) {
      console.error('Error fetching user profiles:', error);
    }
  };

  const handleRefresh = async () => {
    toast.success('Refreshing leads...');
    await fetchLeads();
  };

  const handleStatusChange = async (leadId: string, newStatus: string) => {
    setUpdatingLeadId(leadId);
    
    try {
      const result = await updateLeadStatus(leadId, newStatus);
      if (result.success) {
        // Update local state
        setLeads(prevLeads => 
          prevLeads.map(lead => 
            lead.id === leadId ? { ...lead, status: newStatus } : lead
          )
        );
        toast.success('Lead status updated successfully');
      } else {
        throw new Error(result.error || 'Failed to update status');
      }
    } catch (error: any) {
      console.error('Error updating lead status:', error);
      toast.error(error.message || 'Failed to update lead status');
    } finally {
      setUpdatingLeadId(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingLead(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    if (editingLead) {
      setEditingLead({
        ...editingLead,
        [e.target.name]: e.target.value,
      });
    }
  };

  const handleSaveEdit = async () => {
    if (!editingLead) return;

    setUpdatingLeadId(editingLead.id);
    try {
      // Ensure we only send valid fields from the Lead interface
      const {
        id,
        created_at,
        updated_at,
        ...updateData
      } = editingLead;

      const result = await updateLead(editingLead.id, updateData);

      if (result.success && result.data) {
        setLeads(prevLeads =>
          prevLeads.map(lead =>
            lead.id === editingLead.id ? { ...lead, ...result.data } : lead
          )
        );
        toast.success('Lead details updated successfully');
        setEditingLead(null);
      } else {
        throw new Error(result.error || 'Failed to update lead details');
      }
    } catch (error: any) {
      console.error('Error updating lead details:', error);
      toast.error(error.message || 'Failed to update lead details');
    } finally {
      setUpdatingLeadId(null);
    }
  };

  const handleWhatsAppClick = (phone: string, customerName: string, status?: string) => {
    if (!phone) {
      toast.error('No phone number available');
      return;
    }
    const whatsappPhone = getWhatsAppFormattedNumber(phone);

    if (!whatsappPhone || whatsappPhone.length < 10) { // Basic validation for WhatsApp
      toast.error('Invalid phone number for WhatsApp. Please check the number format.');
      return;
    }
    let message = '';
    switch ((status || '').toUpperCase()) {
      case 'FOLLOW-UP':
      case 'FOLLOW UP':
        message = `Hi ${customerName},

Just following up regarding your trip inquiry. Do you have any update or questions?

- TripXplo Team`;
        break;
      case 'QUOTE SENT':
        message = `Hi ${customerName},

We have sent you a quote for your trip. Please check and let us know if you have any questions!

- TripXplo Team`;
        break;
      case 'BOOKED WITH US':
        message = `Hi ${customerName},

Thank you for booking your trip with us! We look forward to serving you.

- TripXplo Team`;
        break;
      default:
        message = `Hi ${customerName},

Thank you for your interest in TripXplo. Let us know if you have any questions!

- TripXplo Team`;
    }
    const encodedMsg = encodeURIComponent(message);
    window.open(`https://wa.me/${whatsappPhone}?text=${encodedMsg}`, '_blank');
  };

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = !searchTerm || 
      lead.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.destination?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.phone?.includes(searchTerm) ||
      lead.package_type?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.departure_city?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'ALL' || lead.status === statusFilter;
    const matchesPackageType = packageTypeFilter === 'ALL' || lead.package_type === packageTypeFilter;
    const matchesMonth = monthFilter === 'ALL' || 
      (lead.travel_date && new Date(lead.travel_date.split('-')[1] + ' 1, ' + lead.travel_date.split('-')[2]).getMonth() + 1 === parseInt(monthFilter));
    const matchesDestination = destinationFilter === 'ALL' || lead.destination?.toLowerCase().includes(destinationFilter.toLowerCase());
    const matchesNights = nightsFilter === 'ALL' || (lead.nights !== undefined && lead.nights === parseInt(nightsFilter));
    const matchesAdults = adultsFilter === '' || (lead.adults !== undefined && lead.adults === parseInt(adultsFilter));
    const matchesChildren = childrenFilter === '' || (lead.children !== undefined && lead.children === parseInt(childrenFilter));
    const matchesInfants = infantsFilter === '' || (lead.infants !== undefined && lead.infants === parseInt(infantsFilter));
    const matchesAssignedTo = assignedToFilter === 'ALL' || lead.assigned_to === assignedToFilter;

    return matchesSearch && matchesStatus && matchesPackageType && matchesMonth && matchesDestination && matchesNights && matchesAdults && matchesChildren && matchesInfants && matchesAssignedTo;
  });

  // Check if lead is upcoming (within next 2 months)
  const isUpcomingLead = (travelDate?: string) => {
    if (!travelDate) return false;
    try {
      const travel = new Date(travelDate);
      const now = new Date();
      const twoMonthsFromNow = new Date(now.getFullYear(), now.getMonth() + 2, now.getDate());
      return travel >= now && travel <= twoMonthsFromNow;
    } catch {
      return false;
    }
  };

  // Pagination
  const totalPages = Math.ceil(filteredLeads.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedLeads = filteredLeads.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
  };

  const fetchQuotesForCustomers = async () => {
    try {
      console.log('Fetching shared quotes for customers...');
      const quoteSupabase = await getQuoteClient();

      const customerPhones = leads
        .map(l => normalizePhoneNumber(l.phone || ''))
        .filter(Boolean);

      if (customerPhones.length === 0) {
        setQuotesData({});
        return;
      }

      const { data: allSummaries, error } = await quoteSupabase
        .from('customer_quote_summary')
        .select('customer_phone, quote_ids')
        .in('customer_phone', customerPhones);

      if (error) {
        console.error('Error fetching summary by phone:', error);
        return;
      }

      if (!allSummaries) {
        setQuotesData({});
        return;
      }

      const uniqueSummaryData = Array.from(new Map(allSummaries.map(item => [item.customer_phone, item])).values());

      if (uniqueSummaryData.length === 0) {
        setQuotesData({});
        return;
      }

      const allQuoteIds = [...new Set(uniqueSummaryData.flatMap(s => s.quote_ids || []))];

      if (allQuoteIds.length === 0) {
        setQuotesData({});
        return;
      }

      const { data: sharedQuotes, error: sharedQuotesError } = await quoteSupabase
        .from('shared_quotes')
        .select('id, quote_hash, quote_id')
        .in('quote_id', allQuoteIds);

      if (sharedQuotesError) {
        console.error('Error fetching shared_quotes:', sharedQuotesError);
        return;
      }

      if (!sharedQuotes) {
        setQuotesData({});
        return;
      }

      const quoteIdsFromShared = sharedQuotes.map(sq => sq.quote_id);
      const { data: quotesData, error: quotesError } = await quoteSupabase
        .from('quotes')
        .select('id, trip_duration, destination')
        .in('id', quoteIdsFromShared);

      if (quotesError) {
        console.error('Error fetching quotes for trip_duration:', quotesError);
        return;
      }

      const quoteInfoMap = new Map(quotesData?.map(q => [q.id, { trip_duration: q.trip_duration, destination: q.destination }]));

      const combinedQuotes = sharedQuotes.map(sq => ({
        ...sq,
        trip_duration: quoteInfoMap.get(sq.quote_id)?.trip_duration,
        destination: quoteInfoMap.get(sq.quote_id)?.destination,
      }));

      if (!sharedQuotes) {
        setQuotesData({});
        return;
      }

      const sharedQuotesMap = new Map(combinedQuotes.map(q => [q.quote_id, q]));

      const quotesMap: { [key: string]: any[] } = {};
      for (const summary of uniqueSummaryData) {
        const key = normalizePhoneNumber(summary.customer_phone);
        if (key && summary.quote_ids) {
          const customerSharedQuotes = summary.quote_ids
            .map((id: string) => sharedQuotesMap.get(id))
            .filter(Boolean);

          if (customerSharedQuotes.length > 0) {
            quotesMap[key] = customerSharedQuotes;
          }
        }
      }
      setQuotesData(quotesMap);
      console.log('Shared quotes grouped by customer:', quotesMap);

    } catch (error) {
      console.error('Error fetching quotes:', error);
    }
  };

  const getQuotesForLead = (lead: Lead) => {
    const customerKey = normalizePhoneNumber(lead.phone || '');
    return quotesData[customerKey] || [];
  };

  const getInsights = () => {
    const totalLeads = leads.length;
    const upcomingLeads = leads.filter(lead => isUpcomingLead(lead.travel_date)).length;
    const statusCounts = LEAD_STATUSES.reduce((acc, status) => {
      acc[status] = leads.filter(lead => lead.status === status).length;
      return acc;
    }, {} as { [key: string]: number });
    
    const themeCounts = ['Honeymoon', 'Couple', 'Family', 'Friends', 'Corporate', 'Adventure', 'Luxury', 'Budget', 'Custom'].reduce((acc, theme) => {
      acc[theme] = leads.filter(lead => lead.package_type === theme).length;
      return acc;
    }, {} as { [key: string]: number });

    return {
      totalLeads,
      upcomingLeads,
      statusCounts,
      themeCounts,
      conversionRate: totalLeads > 0 ? ((statusCounts['BOOKED WITH US'] || 0) / totalLeads * 100).toFixed(1) : '0'
    };
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = date.toLocaleDateString('en-US', { month: 'short' });
      const year = date.getFullYear();
      return `${day}-${month}-${year}`;
    } catch {
      return 'N/A';
    }
  };

  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'NEW LEAD': 'bg-blue-100 text-blue-800 border-blue-200',
      'CALL CUSTOMER': 'bg-purple-100 text-purple-800 border-purple-200',
      'CONTACTED': 'bg-green-100 text-green-800 border-green-200',
      'CALL NOT ANSWERED': 'bg-orange-100 text-orange-800 border-orange-200',
      'NO RESPONSE': 'bg-red-100 text-red-800 border-red-200',
      'QUOTE SENT': 'bg-cyan-100 text-cyan-800 border-cyan-200',
      'APPROVED': 'bg-emerald-100 text-emerald-800 border-emerald-200',
      'BOOKED WITH US': 'bg-green-100 text-green-800 border-green-200',
      'NOT INTERESTED': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[status] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const formatDateTime = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      // Format: 24-Aug-2024, 10:30 AM
      return date.toLocaleString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      }).replace(',', '');
    } catch {
      return 'Invalid Date';
    }
  };

  const formatPhoneNumber = (phone?: string) => {
    if (!phone) return 'N/A';

    // Remove all non-digit characters, except for a leading '+'
    let cleaned = phone.replace(/[^+\d]/g, '');

    // If it starts with '++', remove one '+'
    if (cleaned.startsWith('++')) {
      cleaned = cleaned.substring(1);
    }

    // If it starts with '+'
    if (cleaned.startsWith('+')) {
      // Check if it's an Indian number (+91 followed by 10 digits)
      const indianMatch = cleaned.match(/^\+91(\d{10})$/);
      if (indianMatch) {
        return `+91 ${indianMatch[1].substring(0, 5)} ${indianMatch[1].substring(5, 10)}`;
      }

      // For other international numbers
      // Try to extract country code (1 to 3 digits after '+')
      const internationalMatch = cleaned.match(/^(\+\d{1,3})(\d.*)$/);
      if (internationalMatch) {
        const countryCode = internationalMatch[1];
        let numberPart = internationalMatch[2];

        // Add spaces to the number part for readability
        // Simple grouping: every 3-4 digits
        let formattedNumberPart = '';
        for (let i = 0; i < numberPart.length; i++) {
          formattedNumberPart += numberPart[i];
          if ((i + 1) % 4 === 0 && (i + 1) !== numberPart.length) { // Group by 4s
            formattedNumberPart += ' ';
          }
        }
        return `${countryCode} ${formattedNumberPart.trim()}`;
      }
      // If it starts with '+' but doesn't match the international pattern, return as is (cleaned)
      return cleaned;
    } else {
      // If it doesn't start with '+' and is a 10-digit number, assume Indian
      if (cleaned.length === 10 && /^\d{10}$/.test(cleaned)) {
        return `+91 ${cleaned.substring(0, 5)} ${cleaned.substring(5, 10)}`;
      }
      // For other cases (e.g., local numbers without country code, or invalid formats), return as is
      return cleaned;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error: {error}</div>
        <button
          onClick={fetchLeads}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Leads Table View</h1>
          <p className="text-gray-600 mt-1">Manage all your leads in a spreadsheet-like interface</p>
        </div>
        <button
          onClick={handleRefresh}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-7 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search Name, Destination, Contact "
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
          <div className="relative ">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              value={destinationFilter}
              onChange={(e) => setDestinationFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="ALL">All Destinations</option>
              {uniqueDestinations.map((destination) => (
                <option key={destination} value={destination}>
                  {destination}
                </option>
              ))}
            </select>
          </div>
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm ${getStatusColor(statusFilter)}`}
            >
              <option value="ALL">All Statuses</option>
              {LEAD_STATUSES.map((status) => (
                <option key={status} value={status} className={getStatusColor(status)}>
                  {status}
                </option>
              ))}
            </select>
          </div>
          <div className="relative lg:col-span-1">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            {isNightsCustom ? (
              <input
                type="number"
                placeholder="Enter nights"
                value={nightsFilter === 'ALL' ? '' : nightsFilter}
                onChange={(e) => setNightsFilter(e.target.value)}
                onBlur={() => {
                  if (!nightsFilter || nightsFilter === 'ALL') {
                    setIsNightsCustom(false);
                    setNightsFilter('ALL');
                  }
                }}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                min="1"
                autoFocus
              />
            ) : (
              <select
                value={nightsFilter}
                onChange={(e) => {
                  if (e.target.value === 'CUSTOM') {
                    setIsNightsCustom(true);
                    setNightsFilter('');
                  } else {
                    setNightsFilter(e.target.value);
                  }
                }}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="ALL">All Nights</option>
                {Array.from({ length: 15 }, (_, i) => (
                  <option key={i + 1} value={i + 1}>
                    {i + 1} Night{i + 1 > 1 ? 's' : ''}
                  </option>
                ))}
                <option value="CUSTOM">Custom</option>
              </select>
            )}
          </div>
          <div className="relative lg:col-span-1">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              value={monthFilter}
              onChange={(e) => setMonthFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="ALL">All Months</option>
              {Array.from({ length: 12 }, (_, i) => {
                const month = new Date(0, i).toLocaleString('en-US', { month: 'short' });
                const monthValue = (i + 1).toString().padStart(2, '0');
                return (
                  <option key={monthValue} value={monthValue}>
                    {month}
                  </option>
                );
              })}
            </select>
          </div>
          <div className="relative border border-gray-300 rounded-lg p-2 lg:col-span-2">
            <div className="grid grid-cols-3 gap-2">
              <div className="relative">
                {isAdultsCustom ? (
                  <input
                    type="number"
                    placeholder="Adults"
                    value={adultsFilter}
                    onChange={(e) => setAdultsFilter(e.target.value)}
                    onBlur={() => {
                      if (!adultsFilter) {
                        setIsAdultsCustom(false);
                      }
                    }}
                    className="w-full px-2 py-1 border border-gray-200 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    min="0"
                    autoFocus
                  />
                ) : (
                  <select
                    value={adultsFilter}
                    onChange={(e) => {
                      if (e.target.value === 'CUSTOM') {
                        setIsAdultsCustom(true);
                        setAdultsFilter('');
                      } else {
                        setAdultsFilter(e.target.value);
                      }
                    }}
                    className="w-full px-2 py-1 border border-gray-200 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Adults</option>
                    {Array.from({ length: 15 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {i + 1}
                      </option>
                    ))}
                    <option value="CUSTOM">Custom</option>
                  </select>
                )}
              </div>
              <div className="relative">
                {isChildrenCustom ? (
                  <input
                    type="number"
                    placeholder="Children"
                    value={childrenFilter}
                    onChange={(e) => setChildrenFilter(e.target.value)}
                    onBlur={() => {
                      if (!childrenFilter) {
                        setIsChildrenCustom(false);
                      }
                    }}
                    className="w-full px-2 py-1 border border-gray-200 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    min="0"
                    autoFocus
                  />
                ) : (
                  <select
                    value={childrenFilter}
                    onChange={(e) => {
                      if (e.target.value === 'CUSTOM') {
                        setIsChildrenCustom(true);
                        setChildrenFilter('');
                      } else {
                        setChildrenFilter(e.target.value);
                      }
                    }}
                    className="w-full px-2 py-1 border border-gray-200 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Children</option>
                    {Array.from({ length: 5 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {i + 1}
                      </option>
                    ))}
                    <option value="CUSTOM">Custom</option>
                  </select>
                )}
              </div>
              <div className="relative">
                {isInfantsCustom ? (
                  <input
                    type="number"
                    placeholder="Infants"
                    value={infantsFilter}
                    onChange={(e) => setInfantsFilter(e.target.value)}
                    onBlur={() => {
                      if (!infantsFilter) {
                        setIsInfantsCustom(false);
                      }
                    }}
                    className="w-full px-2 py-1 border border-gray-200 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    min="0"
                    autoFocus
                  />
                ) : (
                  <select
                    value={infantsFilter}
                    onChange={(e) => {
                      if (e.target.value === 'CUSTOM') {
                        setIsInfantsCustom(true);
                        setInfantsFilter('');
                      } else {
                        setInfantsFilter(e.target.value);
                      }
                    }}
                    className="w-full px-2 py-1 border border-gray-200 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Infants</option>
                    {Array.from({ length: 5 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {i + 1}
                      </option>
                    ))}
                    <option value="CUSTOM">Custom</option>
                  </select>
                )}
              </div>
            </div>
          </div>
       {/*   <div className="relative lg:col-span-1">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              value={assignedToFilter}
              onChange={(e) => setAssignedToFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="ALL">All Assignees</option>
              {Object.entries(userProfiles).map(([id, name]) => (
                <option key={id} value={id}>{name}</option>
              ))}
            </select>
          </div> */}
        </div>
        {(searchTerm || statusFilter !== 'ALL' || packageTypeFilter !== 'ALL' || monthFilter !== 'ALL' || destinationFilter !== 'ALL' || nightsFilter !== 'ALL' || adultsFilter !== '' || childrenFilter !== '' || infantsFilter !== '' || assignedToFilter !== 'ALL') && (
            <div className="mt-4 text-right">
                <button
                    onClick={() => {
                        setSearchTerm('');
                        setStatusFilter('ALL');
                        setPackageTypeFilter('ALL');
                        setMonthFilter('ALL');
                        setDestinationFilter('ALL');
                        setNightsFilter('ALL');
                        setAdultsFilter('');
                        setChildrenFilter('');
                        setInfantsFilter('');
                        setAssignedToFilter('ALL');
                        setDestinationFilter('ALL'); // Added this line
                        setIsNightsCustom(false);
                        setIsAdultsCustom(false);
                        setIsChildrenCustom(false);
                        setIsInfantsCustom(false);
                    }}
                    className="text-sm text-blue-600 hover:text-blue-800 transition-colors font-extrabold"
                >
                    Clear Filters
                </button>
            </div>
        )}
      </div>
                <div className="mt-2 pt-2 border-t">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <span className="text-xs font-medium text-gray-700">Conversion Rate</span>
                <span className="text-xs font-bold text-green-600 ml-2">{getInsights().conversionRate}%</span>
              </div>
              <div className="flex items-center">
                <button
                    onClick={() => setZoomLevel(prev => Math.max(50, prev - 5))}
                    className="p-1 rounded-md bg-gray-200 text-gray-600 hover:bg-gray-300 mr-1"
                    title="Zoom Out"
                >
                    <Minus className="w-3 h-3" />
                </button>
                <button
                    onClick={() => setZoomLevel(prev => Math.min(150, prev + 5))}
                    className="p-1 rounded-md bg-gray-200 text-gray-600 hover:bg-gray-300"
                    title="Zoom In"
                >
                    <Plus className="w-3 h-3" />
                </button>
                <span className="ml-2 text-xs text-gray-600">{zoomLevel}%</span>
              </div>
            </div>
          </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table
            className="min-w-full divide-y divide-gray-200"
            style={{
              transform: `scale(${zoomLevel / 100})`,
              transformOrigin: 'top left',
              width: `${100 / (zoomLevel / 100)}%`,
            }}
          >
            <thead className="bg-gray-100">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-r">
                  Customer
                </th>
                <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-r">
                  Destination
                </th>
                <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider min-w-[160px] border-r">
                  Nights & Guests
                </th>
                <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-r">
                  Travel Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider min-w-[210px] border-r">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider min-w-[230px] border-r">
                  Contact
                </th>
                <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider min-w-[120px] border-r">
                  Notes
                </th>
                <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-r">
                  Theme
                </th>
                <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-r">
                  Assigned To
                </th>
                <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-r">
                  Quotes
                </th>
                <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedLeads.map((lead) => {
                const isUpcoming = isUpcomingLead(lead.travel_date);
                const adults = lead.adults ?? 0;
                const children = lead.children ?? 0;
                const infants = lead.infants ?? 0;
                return (
                <tr key={lead.id} className={`group transition-colors ${
                  isUpcoming ? 'bg-yellow-50 hover:bg-yellow-100' : 'hover:bg-gray-50'
                }`}>
                  <td className="px-4 py-4 whitespace-nowrap border-r">
                    <div className="font-medium text-gray-900">{lead.customer_name}</div>
                    <div className="text-sm text-gray-500">{lead.email}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap border-r">
                    <div className="text-sm text-gray-900 font-medium">{lead.destination || 'N/A'}</div>
                    <div className="text-sm text-gray-500">From: {lead.departure_city || 'N/A'}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap border-r">
                    <div className="text-sm text-gray-900 font-medium">{lead.nights || 'N/A'} nights</div>
                    <div className="text-sm text-gray-500">
                      {[
                        adults > 0 ? `${adults} Adult${adults > 1 ? 's' : ''}` : '',
                        children > 0 ? `${children} Child${children > 1 ? 'ren' : ''}` : '',
                        infants > 0 ? `${infants} Infant${infants > 1 ? 's' : ''}` : ''
                      ].filter(Boolean).join(' ')}
                    </div>
                  </td>
                  <td className="px-4 py-4 font-medium whitespace-nowrap text-sm text-gray-900 border-r">
                    {formatDate(lead.travel_date)}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap border-r">
                    <select
                      value={lead.status}
                      onChange={(e) => handleStatusChange(lead.id, e.target.value)}
                      disabled={updatingLeadId === lead.id}
                      className={`w-full text-sm border rounded-md px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${getStatusColor(lead.status)} ${
                        updatingLeadId === lead.id ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                      }`}
                    >
                      {LEAD_STATUSES.map(status => (
                        <option key={status} value={status}>{status}</option>
                      ))}
                    </select>
                    {lead.status === 'CALL CUSTOMER' && lead.scheduled_to && (
                      <div className="text-xs text-gray-600 text-center mt-1">
                        {formatDateTime(lead.scheduled_to)}
                      </div>
                    )}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap border-r">
                    {lead.phone ? (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-900 font-medium">{formatPhoneNumber(lead.phone)}</span>
                        <button
                          onClick={() => handleWhatsAppClick(lead.phone!, lead.customer_name, lead.status)}
                          className="p-1.5 text-green-600 rounded-full hover:bg-green-100 transition-colors"
                          title="Open WhatsApp Chat"
                        >
                          <WhatsAppIcon className="w-7 h-7" />
                        </button>
                        <a
                          href={`tel:${lead.phone}`}
                          className="p-2 text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100"
                          title="Call"
                        >
                          <Phone className="w-5 h-5" />
                        </a>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-400">No phone</span>
                    )}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap border-r">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleAddNote(lead.id, lead.customer_name)}
                        className="p-1 text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors"
                        title="View/Manage Notes"
                      >
                        <Plus className="w-3 h-3" />
                      </button>
                      <div className="flex gap-1">
                        {renderNoteIcons(lead.id, lead.customer_name)}
                        {(leadNotes[lead.id]?.length || 0) > 3 && (
                          <div className="text-xs text-gray-500 ml-1">
                            +{(leadNotes[lead.id]?.length || 0) - 3} more
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap border-r">
                    <div className="text-sm text-gray-900 font-medium">{lead.package_type || 'N/A'}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 border-r">
                    {lead.assigned_to ? userProfiles[lead.assigned_to] || 'Unknown' : 'Unassigned'}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm border-r">
                    {(() => {
                      const customerQuotes = getQuotesForLead(lead);
                      if (customerQuotes.length > 0) {
                        return (
                          <div className="flex flex-col gap-1">
                            {customerQuotes.map((quote, index) => {
                              const link = `${window.location.origin}/shared-quote/${quote.quote_hash}`;
                              return (
                                <a
                                  key={index}
                                  href={link}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:underline"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    navigator.clipboard.writeText(link);
                                    toast.success('Link copied!');
                                    window.open(link, '_blank');
                                  }}
                                >
                                  {quote.destination ? `${quote.destination} (${quote.trip_duration || 'N/A'})` : (quote.trip_duration || 'View Quote')}
                                </a>
                              );
                            })}
                          </div>
                        );
                      } else {
                        return <span className="text-xs text-gray-400">No saved quotes</span>;
                      }
                    })()}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-center">
                    <div className="relative inline-block text-left">
                      <button
                        onClick={() => setOpenDropdown(openDropdown === lead.id ? null : lead.id)}
                        className="p-2 rounded-full hover:bg-gray-200 transition-colors"
                      >
                        <MoreVertical className="w-5 h-5 text-gray-600" />
                      </button>
                      {openDropdown === lead.id && (
                        <div
                          className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                        >
                          <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                            <button
                              onClick={() => {
                                setEditingLead(lead);
                                setOpenDropdown(null);
                              }}
                              className="w-full text-left flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                              role="menuitem"
                            >
                              <Edit className="w-4 h-4" />
                              Edit Lead
                            </button>
                            <button
                              onClick={() => {
                                handleDeleteLead(lead.id);
                                setOpenDropdown(null);
                              }}
                              className="w-full text-left flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700"
                              role="menuitem"
                            >
                              <Trash2 className="w-4 h-4" />
                              Delete Lead
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              );})}
            </tbody>
          </table>
        </div>
        
        {filteredLeads.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-2">No leads found</div>
            <div className="text-sm text-gray-400">
              {searchTerm || statusFilter !== 'ALL' || packageTypeFilter !== 'ALL'
                ? 'Try adjusting your filters' 
                : 'Create your first lead to get started'}
            </div>
          </div>
        )}
      </div>

      {/* Cards View (Mobile) */}
      <div
        className="md:hidden space-y-4"
        style={{
          transform: `scale(${zoomLevel / 100})`,
          transformOrigin: 'top left',
          width: `${100 / (zoomLevel / 100)}%`,
        }}
      >
        {paginatedLeads.map((lead) => {
          const isUpcoming = isUpcomingLead(lead.travel_date);
          return (
            <div key={lead.id} className={`bg-white rounded-lg shadow-sm border ${isUpcoming ? 'border-yellow-400' : 'border-gray-200'} p-4`}>
              <div className="flex flex-col space-y-3">
                <div className="flex justify-between items-start">
                  <div className="flex-1 min-w-0 mr-2">
                    <p className="font-bold text-lg text-gray-900 truncate">{lead.customer_name}</p>
                    <p className="text-sm text-gray-600 truncate">{lead.destination || 'N/A'}</p>
                  </div>
                  <div className="flex-shrink-0 ml-2">
                    <div className={`text-xs text-center font-semibold px-3 py-2 rounded-md whitespace-nowrap ${getStatusColor(lead.status)}`}>
                      {lead.status}
                    </div>
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Travel Date:</span>
                    <span className="font-medium text-gray-800">{formatDate(lead.travel_date)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Manager:</span>
                    <span className="font-medium text-gray-800 truncate max-w-[150px]">{lead.assigned_to ? userProfiles[lead.assigned_to] || 'Unknown' : 'Unassigned'}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Phone:</span>
                    {lead.phone ? (
                      <div className="flex items-center gap-2">
                        <a href={`tel:${lead.phone}`} className="font-medium text-blue-600 hover:underline text-xs">{formatPhoneNumber(lead.phone)}</a>
                        <button
                          onClick={() => handleWhatsAppClick(lead.phone!, lead.customer_name, lead.status)}
                          className="p-1.5 text-green-600 bg-green-50 rounded-full hover:bg-green-100 transition-colors"
                          title="Open WhatsApp Chat"
                        >
                          <WhatsAppIcon className="w-5 h-5" />
                        </button>
                        <a
                          href={`tel:${lead.phone}`}
                          className="p-1.5 text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors"
                          title="Call"
                        >
                          <Phone className="w-4 h-4" />
                        </a>
                      </div>
                    ) : (
                      <span className="text-gray-400">N/A</span>
                    )}
                  </div>
                </div>

                <div className="pt-3 border-t border-gray-200 flex justify-between items-center">
                  <div className="text-xs text-gray-500">
                    {lead.package_type || 'No theme'}
                  </div>
                  <button
                    onClick={() => setEditingLead(lead)}
                    className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    View / Edit
                  </button>
                </div>
              </div>
            </div>
          );
        })}
        {filteredLeads.length === 0 && (
          <div className="text-center py-12 text-gray-500">No leads found.</div>
        )}
      </div>

      {/* Pagination Controls */}
      {editingLead && (
        <EditLeadModal
          lead={editingLead}
          onClose={handleCancelEdit}
          onSave={handleSaveEdit}
          onInputChange={handleInputChange}
        />
      )}

      {/* Notes Modal */}
      <NotesModal
        isOpen={notesModal.isOpen}
        onClose={closeNotesModal}
        leadId={notesModal.leadId}
        leadName={notesModal.leadName}
        notes={notesModal.notes}
        onSaveNote={handleSaveNote}
        onDeleteNote={handleDeleteNote}
        onLoadNotes={loadNotesForLead}
      />

      {filteredLeads.length > 0 && (
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">
                Showing {startIndex + 1} to {Math.min(endIndex, filteredLeads.length)} of {filteredLeads.length} leads
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Show:</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={50}>50</option>
                  <option value={75}>75</option>
                  <option value={100}>100</option>
                  <option value={150}>150</option>
                  <option value={200}>200</option>
                  <option value={250}>250</option>
                  <option value={300}>300</option>
                </select>
                <span className="text-sm text-gray-600">per page</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`p-2 rounded-lg border ${
                  currentPage === 1 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <ChevronLeft className="w-4 h-4" />
              </button>
              
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  let pageNum = i + 1;
                  if (totalPages > 5 && currentPage > 3) {
                    pageNum = Math.min(currentPage - 2 + i, totalPages);
                    if (currentPage > totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    }
                  }
                  
                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`px-3 py-2 rounded-lg border ${
                        currentPage === pageNum 
                          ? 'bg-blue-600 text-white' 
                          : 'bg-white text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>
              
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || totalPages === 0}
                className={`p-2 rounded-lg border ${
                  currentPage === totalPages || totalPages === 0
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <ChevronRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeadsTable;
